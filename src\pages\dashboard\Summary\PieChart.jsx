import { useEffect, useRef, useState } from "react";
import * as echarts from "echarts";
import GradientBox from "@/components/GradientBox";

import { getSummaryPieChartData } from "@/service/api/summary";

const PieChart = (props) => {
  const chartRef = useRef(null);
  const [myEcharts, setMyEcharts] = useState(null);
  const [chartData, setChartData] = useState([]);

  const initChart = () => {
    // 先销毁已存在的图表实例，避免重复初始化警告
    if (myEcharts) {
      myEcharts.dispose();
    }

    // 检查DOM元素是否存在
    if (!chartRef.current) return;

    let chart = echarts.init(chartRef.current, null, { renderer: "svg" });
    setMyEcharts(chart);

    // 设置初始大小
    chart.resize();

    const options = getOptions(chartData);
    chart.setOption(options);
  };

  useEffect(() => {
    if (props.retailClientId && props.areaId) {
      getSummaryPieChartData({
        retailClientId: props.retailClientId,
        areaId: props.areaId,
      })
        .then((res) => {
          if (res.code == "00000000" && res.data !== null) {
            setChartData(res.data);
          } else {
            setChartData([]);
          }
        })
        .catch((error) => {
          console.error("获取饼图数据失败:", error);
          setChartData([]);
        });
    } else {
      setChartData([]);
    }
  }, [props.retailClientId, props.areaId]);
  const handleResize = () => {
    if (myEcharts) {
      myEcharts.resize();
    }
  };
  useEffect(() => {
    // 在组件挂载时进行初始化
    initChart();
    return () => {
      window.removeEventListener("resize", handleResize);
      if (myEcharts) {
        myEcharts.dispose();
        setMyEcharts(null);
      }
    };
  }, []);

  useEffect(() => {
    if (myEcharts === null) {
      initChart();
    } else {
      setTimeout(() => {
        const options = getOptions(chartData);
        myEcharts.setOption(options);
        myEcharts.resize();
      }, 200);
    }
  }, [chartData]);

  // 添加窗口大小变化监听
  useEffect(() => {
    window.addEventListener("resize", handleResize);

    // 清理函数：组件卸载时移除监听器并销毁图表实例
    return () => {
      window.removeEventListener("resize", handleResize);
      if (myEcharts) {
        myEcharts.dispose();
        setMyEcharts(null);
      }
    };
  }, [myEcharts]);

  const getOptions = (data) => {
    let pieData = data.map((item) => {
      return {
        value: item.total,
        name: item.fieldName,
      };
    });
    return {
      color: [
        "#85bc56",
        "#2d3228",
        "#37422d",
        "#405132",
        "#546f3c",
        "#5e7f42",
        "#719d4c",
        "#85bc56",
      ],
      tooltip: {
        trigger: "item",
      },
      series: [
        {
          name: "",
          type: "pie",
          radius: ["40%", "70%"],
          data: pieData,
          label: {
            show: true,
            formatter: "{b}",
            position: "inside",
            color: "#fff",
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
          },
        },
        {
          name: "",
          type: "pie",
          radius: ["20%", "70%"],
          data: pieData,
          label: {
            show: true,
            position: "outside",
            formatter: "{c}",
          },
          labelLine: {
            show: true,
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
          },
        },
      ],
    };
  };
  return (
    <GradientBox style={{ width: "100%", height: "100%" }}>
      <div style={{ width: "100%", height: "100%" }} ref={chartRef}></div>
    </GradientBox>
  );
};

export default PieChart;
