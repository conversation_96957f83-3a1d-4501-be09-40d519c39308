<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>微应用-react</title>

    <!-- qiankun兼容性修复 -->
    <script>
      // 在qiankun环境中预先设置React Refresh相关变量
      if (window.__POWERED_BY_QIANKUN__) {
        // 设置React Refresh相关的空函数
        window.$RefreshReg$ = function () {};
        window.$RefreshSig$ = function () {
          return function (type) {
            return type;
          };
        };
        window.__vite_plugin_react_preamble_installed__ = true; // 设置为true防止重复注入

        // 确保global变量存在
        if (typeof global === "undefined") {
          window.global = window;
        }

        // 设置process.env
        if (typeof process === "undefined") {
          window.process = { env: { NODE_ENV: "development" } };
        }

        // 拦截可能的React Refresh注入
        const originalCreateElement = document.createElement;
        document.createElement = function (tagName) {
          const element = originalCreateElement.call(this, tagName);
          if (tagName.toLowerCase() === "script") {
            const originalSetAttribute = element.setAttribute;
            element.setAttribute = function (name, value) {
              // 阻止React Refresh相关脚本的执行
              if (
                name === "src" &&
                value &&
                value.includes("/@react-refresh")
              ) {
                return;
              }
              return originalSetAttribute.call(this, name, value);
            };
          }
          return element;
        };
      }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
