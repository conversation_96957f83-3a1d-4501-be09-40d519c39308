import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TextField,
  Grid,
  Link,
  OutlinedInput,
  Stack,
} from "@mui/material";
import { useState, useEffect, useRef, useMemo } from "react";
import {
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";
import { useFormik } from "formik";
import MainCard from "@/components/MainCard";
import MaterialReactTable from "material-react-table";
import { tableI18n } from "@/utils/tableLang";
import ZKSelect from "@/components/ZKSelect";
import {
  listTree,
  saveGroup,
  updateGroup,
  deleteGroup,
} from "@/service/api/materialGroup";
import AuthButton from "@/components/AuthButton";
import GroupForm from "./components/GroupForm";
import { toast } from "react-toastify";
import { removeEmpty } from "@/utils/StringUtils";
import { useTranslation } from "react-i18next";
import { useConfirm } from "@/components/zkconfirm";

const GroupDialog = ({ merchantList }) => {
  const confirm = useConfirm();
  const [formOpen, setFormOpen] = useState(false);
  const [openTable, setOpenTable] = useState(false);
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 分页的参数
  // 查询参数
  const requestParams = useRef(null);
  const { t } = useTranslation();
  const [currentRowId, setCurrentRowId] = useState(false);
  const [rowCount, setRowCount] = useState(0);
  const [rowSelection, setRowSelection] = useState([]);
  const [isError, setIsError] = useState(false);
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const columns = useMemo(
    () => [
      {
        accessorKey: "name", //access nested data with dot notation
        header: t("common.common_material_category_table_column_name"),
        //列头点击相关事件关闭
        enableColumnActions: false,
        enableSorting: false,
        // size: '200',
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.name} placement="top">
              <Typography className="textSpace">{row.original.name}</Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "departmentName", //access nested data with dot notation
        header: t("common.common_material_category_table_column_merchant"),
        //列头点击相关事件关闭
        enableColumnActions: false,
        enableSorting: false,
        // size: '200',
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.departmentName} placement="top">
              <Typography className="textSpace">
                {row.original.departmentName}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "sort", //access nested data with dot notation
        header: t("common.common_material_category_table_column_sort"),
        //列头点击相关事件关闭
        enableColumnActions: false,
        enableSorting: false,
      },
    ],
    []
  );
  const handleClose = () => {
    setOpenTable(false);
    setRowSelection([]);
    setIsError(false);
  };
  const handleButtonClick = () => {
    setOpenTable(true);
  };
  // 构建参数
  const buildParams = () => {
    const params = {
      // page: pagination.pageIndex + 1,
      // pageSize: pagination.pageSize,
      ...requestParams.current,
    };
    return params;
  };
  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    await listTree(buildParams())
      .then((res) => {
        // // 设置数据
        setData(res.data);
        // // 设置总记录数
        setRowCount(res.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };

  useEffect(() => {
    if (openTable) {
      getTableData();
    }
  }, [openTable]);

  const handlesaveGroup = async (values) => {
    return await saveGroup(values)
      .then((res) => {
        toast.success(res?.message);
        getTableData();
        return Promise.resolve(true);
      })
      .catch(() => {
        return Promise.reject(false);
      });
  };
  const handleupdateGroup = async (values) => {
    return await updateGroup(values)
      .then((res) => {
        console.log(res);
        toast.success(res?.message);
        getTableData();
        return Promise.resolve(true);
      })
      .catch(() => {
        return Promise.reject(false);
      });
  };

  useEffect(() => {
    // 发请求
    getTableData();
    setRowSelection([]);
  }, [location]);
  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      name: "",
      merchantId: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        // 拷贝value值
        const tempValue = { ...values };
        await removeEmpty(tempValue);
        requestParams.current = tempValue;
        getTableData();
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });
  // 重置查询
  const resetQuery = () => {
    // 重置表单
    queryFormik.resetForm();
    requestParams.current = null;
    getTableData();
  };
  // 删除
  const handleDel = (values) => {
    const ids = [values.original.id];
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("common.common_material_remove_one_tips", {
        name: values.original.name,
      }),
    }).then(() => {
      deleteGroup(ids).then((res) => {
        setRowSelection([]);
        toast.success(res.message);
        getTableData();
      });
    });
  };
  //批量删除
  const batchDel = (values) => {
    let selectDatas = values.getSelectedRowModel().flatRows;
    if (selectDatas.length == 0) {
      toast.error(t("common.common_check_data"));
      return;
    }
    let params = [];
    let names = [];
    for (let data of selectDatas) {
      let ori = data.original;
      names.push(ori.name);
      params.push(ori.id);
    }
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("common.common_material_remove_more_tips", {
        names: names,
      }),
    }).then(() => {
      deleteGroup(params)
        .then((res) => {
          toast.success(res.message);
          //重置复选框
          setRowSelection([]);
          getTableData();
        })
        .catch((res) => {});
    });
  };
  return (
    <>
      <Tooltip title={t("common.common_material_category")} placement="top">
        <Button variant="contained" onClick={handleButtonClick}>
          {t("common.common_material_category")}
        </Button>
      </Tooltip>
      <BootstrapDialog
        open={openTable}
        maxWidth={"md"}
        fullWidth
        onClose={handleClose}>
        <BootstrapDialogTitle onClose={handleClose}>
          <Typography variant="h4" component="p">
            {t("common.common_material_category")}
          </Typography>
        </BootstrapDialogTitle>
        <BootstrapContent dividers>
          <MainCard style={{ marginBottom: 6 }}>
            <form noValidate onSubmit={queryFormik.handleSubmit}>
              <Grid
                container
                direction="row"
                justifyContent="flex-start"
                alignItems="center"
                spacing={1}>
                <Grid item xs={3}>
                  <OutlinedInput
                    label={t("common.common_material_category_name")}
                    size="small"
                    name="name"
                    value={queryFormik.values.name}
                    type="text"
                    onBlur={queryFormik.handleBlur}
                    onChange={queryFormik.handleChange}
                    fullWidth
                    placeholder={t(
                      "common.common_material_category_name_please"
                    )}
                  />
                </Grid>
                <Grid item xs={3}>
                  <ZKSelect
                    id="merchantId"
                    size="small"
                    name="merchantId"
                    value={queryFormik.values.merchantId}
                    options={merchantList}
                    onClear={() => {
                      queryFormik.setFieldValue("merchantId", "");
                    }}
                    onBlur={queryFormik.handleBlur}
                    onChange={queryFormik.handleChange}
                    type="text"
                    menuWidth={200}
                    placeholder={t("ips.ips_select_merchant")}
                  />
                </Grid>
                <Grid item xs={2}>
                  <Stack
                    direction="row"
                    justifyContent="flex-start"
                    alignItems="flex-start"
                    spacing={2}>
                    <Button
                      disableElevation
                      type="submit"
                      variant="contained"
                      size="small">
                      {t("common.common_table_query")}
                    </Button>
                    <Button
                      disableElevation
                      variant="outlined"
                      color="info"
                      onClick={resetQuery}
                      sx={{
                        minWidth: "90px",
                      }}
                      size="small">
                      {t("common.common_op_reset")}
                    </Button>
                  </Stack>
                </Grid>
              </Grid>
            </form>
          </MainCard>
          <MaterialReactTable
            state={{
              // 分页参数
              // pagination,
              // 重新拉取
              showProgressBars: isRefetching,
              showAlertBanner: isError,
              enableSorting: true,
              columnPinning: { right: ["mrt-row-actions"] },
              // sorting,
              rowSelection,
              isLoading,
            }}
            filterFromLeafRows
            enableExpanding
            muiTablePaperProps={{
              elevation: 0,
              sx: {
                borderRadius: "5px",
                border: "1px solid #f0f0f0",
              },
            }}
            renderToolbarInternalActions={({ table }) => {
              return <></>;
            }}
            muiTableHeadRowProps={{
              sx: { backgroundColor: "#fafafa", boxShadow: "none" },
            }}
            // 关闭过滤搜素
            enableColumnFilters={true}
            // 关闭排序
            enableSorting={false}
            // 布局方式
            layoutMode="grid"
            enableColumnActions={false}
            // enableMultiRowSelection={false}
            // 开启列对齐
            muiTableHeadCellProps={{
              sx: {
                "& .Mui-TableHeadCell-Content": {
                  justifyContent: "space-between",
                },
              },
            }}
            // 解决列太多宽度太长问题
            enableColumnResizing
            // enablePinning
            // 初始化状态
            // initialState={{ columnVisibility: { createTime: true } }}
            muiToolbarAlertBannerProps={
              isError
                ? {
                    color: "error",
                    children: "Error loading data",
                  }
                : undefined
            }
            //行选中
            onRowSelectionChange={setRowSelection}
            muiTableBodyRowProps={({ row }) => ({
              onClick: row.getToggleSelectedHandler(),
              sx: { cursor: "pointer" },
            })}
            // 开启多选
            enableRowSelection
            getRowId={(row) => row.id}
            // 列数
            rowCount={rowCount}
            // 固定头部
            enableStickyHeader
            // 处理表格高度
            // muiTableContainerProps={{ sx: { maxHeight: "620px" } }}
            // 设置背景颜色
            muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
            muiTableProps={{
              sx: { backgroundColor: "white", tableLayout: "fixed" },
            }}
            muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
            muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
            // 分页回调函数
            // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
            manualFiltering
            manualSorting
            enablePagination={false}
            // 排序
            // onSortingChange={setSorting}
            // 开启分页
            // 列定义
            columns={columns}
            // 数据
            data={data}
            // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
            localization={tableI18n}
            // 自定义表头按钮
            renderTopToolbarCustomActions={({ table }) => {
              return (
                <Stack direction={"row"} spacing={1}>
                  <AuthButton button="resource:materialGroup:add">
                    <Button
                      variant="contained"
                      onClick={() => {
                        setFormOpen(true);
                      }}>
                      {t("common.common_material_category_add_button")}
                    </Button>
                  </AuthButton>
                  <AuthButton button="resource:materialGroup:delete">
                    <Button
                      disabled={
                        !table.getIsSomeRowsSelected() &&
                        !table.getIsAllRowsSelected()
                      }
                      variant="contained"
                      color="error"
                      onClick={() => batchDel(table)}>
                      {t("common.common_op_batch_del")}
                    </Button>
                  </AuthButton>
                </Stack>
              );
            }}
            // 多选底部提示
            positionToolbarAlertBanner="none"
            displayColumnDefOptions={{
              "mrt-row-actions": {
                header: t("common.common_relatedOp"), //change header text
                size: 100,
              },
            }}
            // 开启action操作
            enableRowActions
            // action操作位置
            positionActionsColumn="last"
            renderRowActions={({ cell, row, table }) => {
              return (
                <>
                  <Stack direction="row" spacing={2} alignItems="center">
                    <AuthButton button="resource:materialGroup:update">
                      <Link
                        component="button"
                        underline="none"
                        onClick={() => {
                          setCurrentRowId(row?.original?.id);
                          setFormOpen(true);
                        }}>
                        {t("common.common_op_edit")}
                      </Link>
                    </AuthButton>
                    <AuthButton button="resource:materialGroup:delete">
                      <Link
                        component="button"
                        underline="none"
                        color="error"
                        onClick={() => handleDel(row)}>
                        {t("common.common_op_del")}
                      </Link>
                    </AuthButton>
                  </Stack>
                </>
              );
            }}
          />
        </BootstrapContent>
      </BootstrapDialog>
      <GroupForm
        currentRowId={currentRowId}
        open={formOpen}
        merchantList={merchantList}
        onClose={() => {
          setFormOpen(false);
          setCurrentRowId(undefined);
        }}
        onSubmit={async (values) => {
          return values?.id
            ? await handleupdateGroup(values)
            : await handlesaveGroup(values);
        }}
      />
    </>
  );
};
export default GroupDialog;
