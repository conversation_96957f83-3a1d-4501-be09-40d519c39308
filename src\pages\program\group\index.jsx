import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
  TextField,
  Grid,
  OutlinedInput,
  Stack,
} from "@mui/material";
import { useState, useEffect, useRef, useMemo } from "react";
import {
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";
import { useFormik } from "formik";
import MainCard from "@/components/MainCard";
import ZKSelect from "@/components/ZKSelect";
import {
  listTree,
  saveGroup,
  updateGroup,
  deleteGroup,
} from "@/service/api/materialGroup";

import GroupForm from "./components/GroupForm";
import { toast } from "react-toastify";
import { removeEmpty } from "@/utils/StringUtils";
import { useTranslation } from "react-i18next";
import { useConfirm } from "@/components/zkconfirm";
import TreeTable from "./components/TreeTable";

const GroupDialog = ({ merchantList }) => {
  const confirm = useConfirm();
  const [formOpen, setFormOpen] = useState(false);
  const [openTable, setOpenTable] = useState(false);
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 查询参数
  const requestParams = useRef(null);
  const { t } = useTranslation();
  const [currentRowId, setCurrentRowId] = useState(false);
  const [rowCount, setRowCount] = useState(0);
  const [rowSelection, setRowSelection] = useState([]);
  const [isError, setIsError] = useState(false);
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const columns = useMemo(
    () => [
      {
        accessorKey: "name", //access nested data with dot notation
        header: t("common.common_material_category_table_column_name"),
        //列头点击相关事件关闭
        enableColumnActions: false,
        enableSorting: false,
        // size: '200',
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.name} placement="top">
              <Typography className="textSpace">{row.original.name}</Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "departmentName", //access nested data with dot notation
        header: t("common.common_material_category_table_column_merchant"),
        //列头点击相关事件关闭
        enableColumnActions: false,
        enableSorting: false,
        // size: '200',
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.departmentName} placement="top">
              <Typography className="textSpace">
                {row.original.departmentName}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "sort", //access nested data with dot notation
        header: t("common.common_material_category_table_column_sort"),
        //列头点击相关事件关闭
        enableColumnActions: false,
        enableSorting: false,
      },
    ],
    []
  );
  const handleClose = () => {
    setOpenTable(false);
  };
  const handleButtonClick = () => {
    setOpenTable(true);
  };
  // 构建参数
  const buildParams = () => {
    const params = {
      // page: pagination.pageIndex + 1,
      // pageSize: pagination.pageSize,
      ...requestParams.current,
    };
    return params;
  };
  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    await listTree(buildParams())
      .then((res) => {
        // // 设置数据
        setData(res.data);
        // // 设置总记录数
        setRowCount(res.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };

  useEffect(() => {
    if (openTable) {
      getTableData();
    }
  }, [openTable]);

  const handlesaveGroup = async (values) => {
    return await saveGroup(values)
      .then((res) => {
        toast.success(res?.message);
        getTableData();
        return Promise.resolve(true);
      })
      .catch(() => {
        return Promise.reject(false);
      });
  };
  const handleupdateGroup = async (values) => {
    return await updateGroup(values)
      .then((res) => {
        console.log(res);
        toast.success(res?.message);
        getTableData();
        return Promise.resolve(true);
      })
      .catch(() => {
        return Promise.reject(false);
      });
  };

  useEffect(() => {
    // 发请求
    getTableData();
  }, []);
  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      name: "",
      merchantId: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        // 拷贝value值
        const tempValue = { ...values };
        await removeEmpty(tempValue);
        requestParams.current = tempValue;
        getTableData();
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });
  // 重置查询
  const resetQuery = () => {
    // 重置表单
    queryFormik.resetForm();
    requestParams.current = null;
    getTableData();
  };
  // 删除
  const handleDel = (values) => {
    const ids = [values.original.id];
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("common.common_material_remove_one_tips", {
        name: values.original.name,
      }),
    }).then(() => {
      deleteGroup(ids).then((res) => {
        toast.success(res.message);
        getTableData();
      });
    });
  };
  //批量删除
  const batchDel = (values) => {
    let selectDatas = values.getSelectedRowModel().flatRows;
    if (selectDatas.length == 0) {
      toast.error(t("common.common_check_data"));
      return;
    }
    let params = [];
    let names = [];
    for (let data of selectDatas) {
      let ori = data.original;
      names.push(ori.name);
      params.push(ori.id);
    }
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("common.common_material_remove_more_tips", {
        names: names,
      }),
    }).then(() => {
      deleteGroup(params)
        .then((res) => {
          toast.success(res.message);
          getTableData();
        })
        .catch(() => {});
    });
  };
  return (
    <>
      <Tooltip title={t("common.common_material_category")} placement="top">
        <Button variant="contained" onClick={handleButtonClick}>
          {t("common.common_material_category")}
        </Button>
      </Tooltip>
      <BootstrapDialog
        open={openTable}
        maxWidth={"md"}
        fullWidth
        onClose={handleClose}>
        <BootstrapDialogTitle onClose={handleClose}>
          <Typography variant="h4" component="p">
            {t("common.common_material_category")}
          </Typography>
        </BootstrapDialogTitle>
        <BootstrapContent dividers>
          <MainCard style={{ marginBottom: 6 }}>
            <form noValidate onSubmit={queryFormik.handleSubmit}>
              <Grid
                container
                direction="row"
                justifyContent="flex-start"
                alignItems="center"
                spacing={1}>
                <Grid item xs={3}>
                  <OutlinedInput
                    label={t("common.common_material_category_name")}
                    size="small"
                    name="name"
                    value={queryFormik.values.name}
                    type="text"
                    onBlur={queryFormik.handleBlur}
                    onChange={queryFormik.handleChange}
                    fullWidth
                    placeholder={t(
                      "common.common_material_category_name_please"
                    )}
                  />
                </Grid>
                <Grid item xs={3}>
                  <ZKSelect
                    id="merchantId"
                    size="small"
                    name="merchantId"
                    value={queryFormik.values.merchantId}
                    options={merchantList}
                    onClear={() => {
                      queryFormik.setFieldValue("merchantId", "");
                    }}
                    onBlur={queryFormik.handleBlur}
                    onChange={queryFormik.handleChange}
                    type="text"
                    menuWidth={200}
                    placeholder={t("ips.ips_select_merchant")}
                  />
                </Grid>
                <Grid item xs={2}>
                  <Stack
                    direction="row"
                    justifyContent="flex-start"
                    alignItems="flex-start"
                    spacing={2}>
                    <Button
                      disableElevation
                      type="submit"
                      variant="contained"
                      size="small">
                      {t("common.common_table_query")}
                    </Button>
                    <Button
                      disableElevation
                      variant="outlined"
                      color="info"
                      onClick={resetQuery}
                      sx={{
                        minWidth: "90px",
                      }}
                      size="small">
                      {t("common.common_op_reset")}
                    </Button>
                  </Stack>
                </Grid>
              </Grid>
            </form>
          </MainCard>
          <TreeTable
            data={data}
            columns={columns}
            loading={isLoading}
            onEdit={(node) => {
              setCurrentRowId(node.id);
              setFormOpen(true);
            }}
            onDelete={(node) => {
              handleDel({ original: node });
            }}
            onAdd={() => {
              setFormOpen(true);
            }}
            onBatchDelete={(selectedIds) => {
              // 构造批量删除所需的数据结构
              const mockTable = {
                getSelectedRowModel: () => ({
                  flatRows: selectedIds.map((id) => {
                    // 递归查找节点
                    const findNode = (nodes) => {
                      for (const node of nodes) {
                        if (node.id === id) return node;
                        if (node.children) {
                          const found = findNode(node.children);
                          if (found) return found;
                        }
                      }
                      return null;
                    };
                    const node = findNode(data);
                    return { original: node };
                  }),
                }),
              };
              batchDel(mockTable);
            }}
          />
        </BootstrapContent>
      </BootstrapDialog>

      <GroupForm
        currentRowId={currentRowId}
        open={formOpen}
        merchantList={merchantList}
        onClose={() => {
          setFormOpen(false);
          setCurrentRowId(undefined);
        }}
        onSubmit={async (values) => {
          return values?.id
            ? await handleupdateGroup(values)
            : await handlesaveGroup(values);
        }}
      />
    </>
  );
};
export default GroupDialog;
