import React, { useState, useCallback } from "react";
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Typography,
  Checkbox,
  Toolbar,
  Stack,
  Button,
  Tooltip,
  Link,
  Collapse,
} from "@mui/material";
import {
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import AuthButton from "@/components/AuthButton";

// 单个树节点组件
const TreeNode = ({
  node,
  level = 0,
  onEdit,
  onDelete,
  onToggleExpand,
  onToggleSelect,
  selectedNodes,
  expandedNodes,
  columns,
}) => {
  const { t } = useTranslation();
  const hasChildren = node.children && node.children.length > 0;
  const isSelected = selectedNodes.has(node.id);
  const isExpanded = expandedNodes.has(node.id);

  const handleToggleExpand = useCallback(() => {
    if (hasChildren) {
      onToggleExpand(node.id);
    }
  }, [hasChildren, node.id, onToggleExpand]);

  const handleToggleSelect = useCallback(
    (event) => {
      event.stopPropagation();
      onToggleSelect(node.id);
    },
    [node.id, onToggleSelect]
  );

  const handleEdit = useCallback(
    (event) => {
      event.stopPropagation();
      onEdit(node);
    },
    [node, onEdit]
  );

  const handleDelete = useCallback(
    (event) => {
      event.stopPropagation();
      onDelete(node);
    },
    [node, onDelete]
  );

  return (
    <>
      <TableRow
        hover
        selected={isSelected}
        sx={{
          "& > *": { borderBottom: "unset" },
          cursor: hasChildren ? "pointer" : "default",
        }}
        onClick={handleToggleExpand}>
        {/* 选择框列 */}
        <TableCell padding="checkbox">
          <Checkbox
            checked={isSelected}
            onChange={handleToggleSelect}
            onClick={(e) => e.stopPropagation()}
          />
        </TableCell>

        {/* 数据列 */}
        {columns.map((column, index) => (
          <TableCell key={column.accessorKey}>
            {index === 0 ? (
              <Box
                sx={{ display: "flex", alignItems: "center", pl: level * 3 }}>
                {hasChildren ? (
                  <IconButton
                    size="small"
                    onClick={handleToggleExpand}
                    sx={{ mr: 1 }}>
                    {isExpanded ? <ExpandMoreIcon /> : <ChevronRightIcon />}
                  </IconButton>
                ) : (
                  <Box sx={{ width: 32, mr: 1 }} />
                )}
                <Tooltip title={node[column.accessorKey]} placement="top">
                  <Typography className="textSpace">
                    {node[column.accessorKey]}
                  </Typography>
                </Tooltip>
              </Box>
            ) : (
              <Tooltip title={node[column.accessorKey]} placement="top">
                <Typography className="textSpace">
                  {node[column.accessorKey]}
                </Typography>
              </Tooltip>
            )}
          </TableCell>
        ))}

        {/* 操作列 */}
        <TableCell>
          <Stack direction="row" spacing={1} alignItems="center">
            <AuthButton button="resource:materialGroup:update">
              <Link component="button" underline="none" onClick={handleEdit}>
                {t("common.common_op_edit")}
              </Link>
            </AuthButton>
            <AuthButton button="resource:materialGroup:delete">
              <Link
                component="button"
                underline="none"
                color="error"
                onClick={handleDelete}>
                {t("common.common_op_del")}
              </Link>
            </AuthButton>
          </Stack>
        </TableCell>
      </TableRow>

      {/* 子节点 */}
      {hasChildren && (
        <TableRow>
          <TableCell
            style={{ paddingBottom: 0, paddingTop: 0 }}
            colSpan={columns.length + 2}>
            <Collapse in={isExpanded} timeout="auto" unmountOnExit>
              <Table size="small">
                <TableBody>
                  {node.children.map((child) => (
                    <TreeNode
                      key={child.id}
                      node={child}
                      level={level + 1}
                      onEdit={onEdit}
                      onDelete={onDelete}
                      onToggleExpand={onToggleExpand}
                      onToggleSelect={onToggleSelect}
                      selectedNodes={selectedNodes}
                      expandedNodes={expandedNodes}
                      columns={columns}
                    />
                  ))}
                </TableBody>
              </Table>
            </Collapse>
          </TableCell>
        </TableRow>
      )}
    </>
  );
};

// 主树表格组件
const TreeTable = ({
  data = [],
  columns = [],
  onEdit,
  onDelete,
  onAdd,
  onBatchDelete,
  loading = false,
  title = "树形表格",
}) => {
  const { t } = useTranslation();
  const [expandedNodes, setExpandedNodes] = useState(new Set());
  const [selectedNodes, setSelectedNodes] = useState(new Set());

  // 切换展开状态
  const handleToggleExpand = useCallback((nodeId) => {
    setExpandedNodes((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  }, []);

  // 切换选择状态
  const handleToggleSelect = useCallback((nodeId) => {
    setSelectedNodes((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  }, []);

  // 全选/取消全选
  const handleSelectAll = useCallback(
    (event) => {
      if (event.target.checked) {
        const getAllNodeIds = (nodes) => {
          let ids = [];
          nodes.forEach((node) => {
            ids.push(node.id);
            if (node.children) {
              ids = ids.concat(getAllNodeIds(node.children));
            }
          });
          return ids;
        };
        setSelectedNodes(new Set(getAllNodeIds(data)));
      } else {
        setSelectedNodes(new Set());
      }
    },
    [data]
  );

  // 批量删除
  const handleBatchDelete = useCallback(() => {
    if (selectedNodes.size > 0 && onBatchDelete) {
      onBatchDelete(Array.from(selectedNodes));
    }
  }, [selectedNodes, onBatchDelete]);

  const isAllSelected =
    selectedNodes.size > 0 && selectedNodes.size === getTotalNodeCount(data);
  const isIndeterminate =
    selectedNodes.size > 0 && selectedNodes.size < getTotalNodeCount(data);

  return (
    <Paper sx={{ width: "100%", overflow: "hidden" }}>
      {/* 工具栏 */}
      <Toolbar sx={{ backgroundColor: "white" }}>
        <Stack direction="row" spacing={1} sx={{ flexGrow: 1 }}>
          <AuthButton button="resource:materialGroup:add">
            <Button variant="contained" onClick={onAdd}>
              {t("common.common_material_category_add_button")}
            </Button>
          </AuthButton>
          <AuthButton button="resource:materialGroup:delete">
            <Button
              variant="contained"
              color="error"
              disabled={selectedNodes.size === 0}
              onClick={handleBatchDelete}>
              {t("common.common_op_batch_del")}
            </Button>
          </AuthButton>
        </Stack>
      </Toolbar>

      {/* 表格 */}
      <TableContainer>
        <Table stickyHeader>
          <TableHead>
            <TableRow sx={{ backgroundColor: "#fafafa" }}>
              <TableCell padding="checkbox">
                <Checkbox
                  indeterminate={isIndeterminate}
                  checked={isAllSelected}
                  onChange={handleSelectAll}
                />
              </TableCell>
              {columns.map((column) => (
                <TableCell key={column.accessorKey}>{column.header}</TableCell>
              ))}
              <TableCell>{t("common.common_relatedOp")}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={columns.length + 2} align="center">
                  <Typography>加载中...</Typography>
                </TableCell>
              </TableRow>
            ) : data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length + 2} align="center">
                  <Typography>暂无数据</Typography>
                </TableCell>
              </TableRow>
            ) : (
              data.map((node) => (
                <TreeNode
                  key={node.id}
                  node={node}
                  level={0}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onToggleExpand={handleToggleExpand}
                  onToggleSelect={handleToggleSelect}
                  selectedNodes={selectedNodes}
                  expandedNodes={expandedNodes}
                  columns={columns}
                />
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  );
};

// 辅助函数：获取总节点数
function getTotalNodeCount(nodes) {
  let count = 0;
  nodes.forEach((node) => {
    count++;
    if (node.children) {
      count += getTotalNodeCount(node.children);
    }
  });
  return count;
}

export default TreeTable;
